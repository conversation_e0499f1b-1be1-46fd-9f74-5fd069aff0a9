# 地图层级问题修复说明

## 问题描述

在微信小程序中，地图组件（`map`）一直处在模态框（modal）的下面，无法正常操作。这是因为：

1. 微信小程序中的 `map` 组件是原生组件，具有特殊的层级处理
2. 模态框的 z-index 设置不当，导致地图被遮挡
3. 地图容器的层级设置不正确

## 修复方案

### 1. 提高地图选择器的 z-index

将地图选择器相关元素的 z-index 设置为最高层级：

```css
/* 地图选择器模态框 */
.map-selector-modal {
  z-index: 9999; /* 原来是 1101 */
}

/* 地图模态框遮罩 */
.map-modal-mask {
  z-index: 10000; /* 原来是 1102 */
}

/* 地图内容容器 */
.map-content {
  z-index: 10001; /* 原来是 1104 */
}

/* 地图中心标记和提示 */
.map-center-marker,
.map-tip {
  z-index: 10002; /* 原来是 1110 */
}
```

### 2. 优化地图容器样式

为地图容器添加必要的样式属性：

```css
.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  z-index: 1;
  background: #f5f5f5; /* 添加背景色 */
}

.location-map {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1; /* 确保地图有正确的层级 */
}
```

### 3. 添加缺失的样式

为地图模态框的关闭按钮添加样式：

```css
.map-modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 20rpx;
}
```

## 层级结构说明

修复后的层级结构（从低到高）：

1. **地址表单模态框**: z-index: 1010-1011
2. **普通模态框**: z-index: 1000-1003
3. **地图选择器**: z-index: 9999-10002 （最高层级）

## 微信小程序原生组件特性

微信小程序中的 `map` 组件是原生组件，具有以下特性：

1. **层级最高**: 原生组件的层级比其他组件高
2. **cover-view 覆盖**: 只能使用 `cover-view` 和 `cover-image` 覆盖原生组件
3. **样式限制**: 某些 CSS 样式对原生组件无效

## 使用的解决方案

1. **使用 cover-view**: 地图上的十字标记和提示文字使用 `cover-view` 实现
2. **高 z-index**: 将整个地图选择器的 z-index 设置得足够高
3. **正确的容器结构**: 确保地图容器有正确的定位和层级

## 验证方法

1. 打开配送页面
2. 点击地址选择
3. 点击"地图选择"按钮
4. 确认地图能正常显示和操作
5. 确认十字标记和提示文字正常显示
6. 确认可以拖动地图和点击选择位置

## 注意事项

1. **z-index 冲突**: 确保地图选择器的 z-index 比其他模态框高
2. **原生组件限制**: 只能使用 cover-view 覆盖地图组件
3. **性能考虑**: 避免过度使用高 z-index 值
4. **兼容性**: 确保在不同设备和微信版本上都能正常工作

## 后续优化建议

1. 统一管理所有模态框的 z-index 值
2. 创建 z-index 常量文件，避免硬编码
3. 考虑使用微信小程序的 `wx.createMapContext()` API 进行更精细的地图控制
4. 添加地图加载状态和错误处理
